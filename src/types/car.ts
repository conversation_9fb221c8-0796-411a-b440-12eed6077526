import { Types } from 'mongoose';

import { AdvancedQueryResult } from './query_results.js';
import { AddressDoc } from './user.js';

export enum GEAR_TYPE {
  MANUAL_TRNASMISSION = 'manual',
  AUTOMATIC_TRNASMISSION = 'automatic',
  ELECTRIC_VEHICLE = 'Electric Vehicle (EV) Single-Speed Gearbox'
}

export enum ENGINE_TYPE {
  V4 = 'v4',
  V6 = 'v6',
  V8 = 'v8'
}

export enum DOOR_COUNT {
  TWO_DOOR = 'two door',
  FOOR_DOOR = 'four door'
}

export enum FUEL_TYPE {
  DIESEL = 'diesel',
  PETROL = 'petrol',
  ELECTRIC = 'electric'
}

export enum SCHEDULE {
  DAY_NIGHT = 'Day/Night',
  DAY_ONLY = 'Day Only',
  NIGHT_ONLY = 'Night Only'
}

export type BookDatesType = {
  startDate: string;
  endDate: string;
  startTime: string;
};

export type CarDoc = {
  _id: Types.ObjectId;
  id: string;
  description: string;
  seatType: string;
  ratingSummary: number;
  rating: Types.ObjectId[];
  engineType: ENGINE_TYPE;
  year: string;
  model: string;
  brand: Types.ObjectId;
  company: Types.ObjectId;
  carModel: Types.ObjectId;
  category: Types.ObjectId;
  address: AddressDoc;
  dailyPrice: number;
  mainImageId?: string;
  dailyMinPrice: number;
  schedule: SCHEDULE;
  booked: boolean;
  bookedDates: BookDatesType[];
  carImages: Types.ObjectId[];
  bookingCount: number;
  numOfDoors: number;
  totalEarned: number;
  isActive: boolean;
  isAvailable: boolean;
  createdAt: Date;
  createdBy: Types.ObjectId;
  updatedBy: Types.ObjectId;
  updatedAt: Date;
};

export type AdvancedUsersQueryResult = AdvancedQueryResult<CarDoc>;
export type CardDocExt = Omit<CarDoc, 'createdBy' | 'updatedBy'> & {
  createdBy: { fullName: string };
  updatedBy?: { fullName: string };
};

export type RegisterCarRequestBody = Omit<CarDoc, '_id' | 'brand' | 'category' | 'createdAt' | 'updatedAt'> & {
  brandId: string;
  categoryId: string;
};

export type UpdateCarRequestBody = Omit<
  CarDoc,
  '_id' | 'brand' | 'category' | 'createdAt' | 'updatedAt' | 'isActive' | 'company' | 'model'
>;

export type FetchCarsRequestBody = {
  pickupAddress: {
    longitude: number;
    latitude: number;
    state: string;
    sortOrder?: string;
    limit?: number;
    skip?: number;
  };
  destinationAddress: {
    longitude: number;
    latitude: number;
    state: string;
  };
  requestTime?: string; // Time when the request is made (HH:mm format)
};
