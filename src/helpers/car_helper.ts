import mongoose, { Schema } from 'mongoose';

import { ESCORT_PRICES, MIN_KM, VAT } from '../libs/consts.js';
import Logger from '../libs/logger.js';
import Booking from '../models/BookingModel/BookingModel.js';
import Rating from '../models/RatingModel/RatingModel.js';
import { BOOKING_STATUS, BookingDoc } from '../types/booking.js';
import { CarDoc, SCHEDULE } from '../types/car.js';
import { AddressDoc } from '../types/client.js';

type RatingSummaryResult = {
  _id: Schema.Types.ObjectId;
  avgRating: number;
};
export const calculateRatingSummary = async (carId: string): Promise<number> => {
  const result: RatingSummaryResult[] = await Rating.aggregate([
    { $match: { carId: new mongoose.Types.ObjectId(carId) } },
    { $group: { _id: '$carId', avgRating: { $avg: '$rating' } } }
  ]);
  const averageRating = result?.[0]?.avgRating ?? 5;
  return averageRating;
};

export const cancelOldBookings = async () => {
  try {
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago
    await Booking.updateMany(
      {
        createdAt: { $lt: twentyFourHoursAgo },
        status: BOOKING_STATUS.IN_PROGRESS
      },
      {
        $set: { status: BOOKING_STATUS.CANCELED }
      }
    );
    Logger.info(`Canceled bookings that were older than 24 hours.`);
  } catch (error) {
    console.error('Error canceling old bookings:', error);
  }
};

export const calculateDistance = (coords1: AddressDoc, coords2: AddressDoc): number => {
  const toRadians = (degrees: number): number => (degrees * Math.PI) / 180;

  const R = 6371; // Radius of Earth in kilometers

  const lat1 = toRadians(coords1.latitude);
  const lon1 = toRadians(coords1.longitude);
  const lat2 = toRadians(coords2.latitude);
  const lon2 = toRadians(coords2.longitude);

  const dLat = lat2 - lat1;
  const dLon = lon2 - lon1;

  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(lat1) * Math.cos(lat2) * Math.sin(dLon / 2) * Math.sin(dLon / 2);

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return R * c; // Distance in kilometers
};

export const isCarAvailableBySchedule = (carSchedule: SCHEDULE, requestTime?: string): boolean => {
  if (!requestTime) {
    return true; // If no time specified, return all cars
  }

  // Parse the request time (HH:mm format)
  const [hours, minutes] = requestTime.split(':').map(Number);
  const requestTimeInMinutes = hours * 60 + minutes;

  // Define time ranges (in minutes from midnight)
  const nightStart = 15 * 60; // 3:00 PM (15:00)
  const nightEnd = 3 * 60; // 3:00 AM (03:00)

  // Determine if the request time is during night hours
  // Night is from 3 PM to 3 AM (next day)
  const isNightTime = requestTimeInMinutes >= nightStart || requestTimeInMinutes <= nightEnd;

  switch (carSchedule) {
    case SCHEDULE.DAY_NIGHT:
      return true; // Available 24/7
    case SCHEDULE.DAY_ONLY:
      return !isNightTime; // Available only during day (3 AM to 3 PM)
    case SCHEDULE.NIGHT_ONLY:
      return isNightTime; // Available only during night (3 PM to 3 AM)
    default:
      return true;
  }
};

export const calculateBookingCharges = (car: CarDoc, newBooking: BookingDoc) => {
  // Ensure minimum of 1 day for booking
  const numOfDays = Math.max(newBooking.numberOfDays || 1, 1);

  // Calculate escort charges only if escorts are requested
  const escortCount = newBooking.escortCount || 0;
  const escortDays = newBooking.escortDays || 0;

  let escortPayments = 0;
  if (escortCount > 0 && escortDays > 0) {
    const escortPrice =
      newBooking.pickupAddress.state.toLowerCase() === newBooking.destinationAddress.state.toLowerCase()
        ? ESCORT_PRICES.withinState
        : ESCORT_PRICES.outsideState;
    escortPayments = escortCount * escortPrice * escortDays;
  }

  const { destinationKilometer, pickupKilometer } = newBooking;

  const totalKilometer = destinationKilometer + pickupKilometer;

  // Calculate base price based on distance and additional stops
  const distancePrice =
    newBooking.hasAdditionalStop || totalKilometer > MIN_KM
      ? car.dailyPrice * numOfDays
      : car.dailyMinPrice * numOfDays;

  // Calculate VAT on the subtotal (distance price + escort payments)
  const subtotal = distancePrice + escortPayments;
  const vatCalculation = subtotal * VAT;

  // Calculate manager's share (80% of distance price, excluding VAT and escort fees)
  const managerTotal = distancePrice * 0.8;

  return {
    total: Number(Math.ceil(subtotal + vatCalculation)),
    managerTotal: Number(managerTotal.toFixed(2)),
    escortsTotal: Number(escortPayments.toFixed(2)),
    vat: Number(vatCalculation.toFixed(2)),
    subtotal: Number(subtotal.toFixed(2)),
    distancePrice: Number(distancePrice.toFixed(2))
  };
};
