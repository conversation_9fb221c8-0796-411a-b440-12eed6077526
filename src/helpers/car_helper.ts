import mongoose, { Schem<PERSON> } from 'mongoose';

import { ESCORT_PRICES, MIN_KM, VAT } from '../libs/consts.js';
import Logger from '../libs/logger.js';
import Booking from '../models/BookingModel/BookingModel.js';
import Rating from '../models/RatingModel/RatingModel.js';
import { BOOKING_STATUS, BookingDoc } from '../types/booking.js';
import { CarDoc } from '../types/car.js';
import { AddressDoc } from '../types/client.js';

type RatingSummaryResult = {
  _id: Schema.Types.ObjectId;
  avgRating: number;
};
export const calculateRatingSummary = async (carId: string): Promise<number> => {
  const result: RatingSummaryResult[] = await Rating.aggregate([
    { $match: { carId: new mongoose.Types.ObjectId(carId) } },
    { $group: { _id: '$carId', avgRating: { $avg: '$rating' } } }
  ]);
  const averageRating = result?.[0]?.avgRating ?? 5;
  return averageRating;
};

export const cancelOldBookings = async () => {
  try {
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago
    await Booking.updateMany(
      {
        createdAt: { $lt: twentyFourHoursAgo },
        status: BOOKING_STATUS.IN_PROGRESS
      },
      {
        $set: { status: BOOKING_STATUS.CANCELED }
      }
    );
    Logger.info(`Canceled bookings that were older than 24 hours.`);
  } catch (error) {
    console.error('Error canceling old bookings:', error);
  }
};

export const calculateDistance = (coords1: AddressDoc, coords2: AddressDoc): number => {
  const toRadians = (degrees: number): number => (degrees * Math.PI) / 180;

  const R = 6371; // Radius of Earth in kilometers

  const lat1 = toRadians(coords1.latitude);
  const lon1 = toRadians(coords1.longitude);
  const lat2 = toRadians(coords2.latitude);
  const lon2 = toRadians(coords2.longitude);

  const dLat = lat2 - lat1;
  const dLon = lon2 - lon1;

  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(lat1) * Math.cos(lat2) * Math.sin(dLon / 2) * Math.sin(dLon / 2);

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return R * c; // Distance in kilometers
};

export const calculateBookingCharges = (car: CarDoc, newBooking: BookingDoc) => {
  const numOfDays = newBooking.numberOfDays;
  const escortPrice =
    newBooking.pickupAddress.state.toLowerCase() === newBooking.destinationAddress.state.toLowerCase()
      ? ESCORT_PRICES.withinState
      : ESCORT_PRICES.outsideState;
  const escortPayments = newBooking.escortCount * escortPrice * newBooking.escortDays;

  const { destinationKilometer, pickupKilometer } = newBooking;

  const totalKilometer = destinationKilometer + pickupKilometer;
  const distancePrice =
    newBooking.hasAdditionalStop || totalKilometer > MIN_KM
      ? car.dailyPrice * numOfDays
      : car.dailyMinPrice * numOfDays;

  const vatCalculation = (distancePrice + escortPayments) * VAT;

  return {
    total: Number(Math.ceil(vatCalculation + distancePrice + escortPayments)),
    managerTotal: distancePrice - (20 / 100) * distancePrice,
    escortsTotal: escortPayments,
    vat: vatCalculation
  };
};
