import Joi from '@hapi/joi';

import { SCHEDULE } from '../types/car.js';

/*
location: {
    type: string,
    coordinates: {
      type: number[], // Longitude first, then Latitude
    }
  };
*/

export const createCarApiValidator = Joi.object({
  year: Joi.string().required(),
  brandId: Joi.string().required(),
  model: Joi.string().required(),
  categoryId: Joi.string().required(),
  address: Joi.object({
    fullAddress: Joi.string().required(),
    longitude: Joi.number().required(),
    latitude: Joi.number().required(),
    state: Joi.string().required(),
    city: Joi.string().required(),
    countryCode: Joi.string().required(),
    country: Joi.string().required(),
    lga: Joi.string(),
    postal: Joi.string(),
    area: Joi.string(),
    neighborhood: Joi.string()
  }).required(),
  dailyPrice: Joi.number().required(),
  dailyMinPrice: Joi.number().required(),
  schedule: Joi.string()
    .valid(...Object.values(SCHEDULE))
    .required(),
  description: Joi.string()
});

export const updateVisibleCarImages = Joi.array().items(
  Joi.object({
    imageId: Joi.string().required(),
    isVisible: Joi.boolean().required()
  })
);

export const updateCarApiValidator = Joi.object({
  address: Joi.object({
    fullAddress: Joi.string().required(),
    longitude: Joi.number().required(),
    latitude: Joi.number().required(),
    state: Joi.string().required(),
    city: Joi.string().required(),
    countryCode: Joi.string().required(),
    country: Joi.string().required(),
    lga: Joi.string(),
    postal: Joi.string(),
    area: Joi.string(),
    neighborhood: Joi.string()
  }),
  dailyPrice: Joi.number(),
  dailyMinPrice: Joi.number(),
  isAvailable: Joi.boolean(),
  schedule: Joi.string().valid(...Object.values(SCHEDULE)),
  description: Joi.string()
});

export const deleteCarImagesApiValidator = Joi.object({
  imageIds: Joi.array().items(Joi.string().required()).required()
});

export const updateMainImageApiValidator = Joi.object({
  imageId: Joi.string().required()
});

export const fetchCarsApiValidator = Joi.object({
  pickupAddress: Joi.object({
    longitude: Joi.number().required(),
    latitude: Joi.number().required(),
    state: Joi.string().required()
  }).required(),
  destinationAddress: Joi.object({
    longitude: Joi.number().required(),
    latitude: Joi.number().required(),
    state: Joi.string().required()
  }).required(),
  requestTime: Joi.string().optional() // Time when the request is made (HH:mm format)
});
